import { useState, useCallback } from 'react'
import { apiService } from '../services/api'
import type { TutorialPlan, VideoStatus } from '../services/api'

interface ProgressStep {
  id: string
  title: string
  status: 'pending' | 'in-progress' | 'completed' | 'error'
  description?: string
}

interface TutorialGenerationState {
  // Current state
  isGenerating: boolean
  currentStep: number
  error: string | null
  
  // Data
  tutorialPlan: TutorialPlan | null
  videoId: string | null
  videoStatus: VideoStatus | null
  
  // Progress tracking
  steps: ProgressStep[]
}

const defaultSteps: ProgressStep[] = [
  {
    id: '1',
    title: 'Analyzing Problem',
    status: 'pending',
    description: 'AI is understanding your coding challenge'
  },
  {
    id: '2',
    title: 'Generating Steps',
    status: 'pending',
    description: 'Creating step-by-step solution plan'
  },
  {
    id: '3',
    title: 'Creating Animation',
    status: 'pending',
    description: 'Building animated tutorial video'
  },
  {
    id: '4',
    title: 'Rendering Video',
    status: 'pending',
    description: 'Finalizing your tutorial'
  }
]

export const useTutorialGeneration = () => {
  const [state, setState] = useState<TutorialGenerationState>({
    isGenerating: false,
    currentStep: 0,
    error: null,
    tutorialPlan: null,
    videoId: null,
    videoStatus: null,
    steps: [...defaultSteps]
  })

  const updateStep = useCallback((stepIndex: number, status: ProgressStep['status']) => {
    setState(prev => ({
      ...prev,
      steps: prev.steps.map((step, index) => 
        index === stepIndex ? { ...step, status } : step
      ),
      currentStep: stepIndex
    }))
  }, [])

  const resetState = useCallback(() => {
    setState({
      isGenerating: false,
      currentStep: 0,
      error: null,
      tutorialPlan: null,
      videoId: null,
      videoStatus: null,
      steps: [...defaultSteps]
    })
  }, [])

  const generateTutorial = useCallback(async (problem: string) => {
    try {
      // Reset state and start generation
      setState(prev => ({
        ...prev,
        isGenerating: true,
        error: null,
        steps: [...defaultSteps]
      }))

      // Step 1: Analyze problem
      updateStep(0, 'in-progress')
      
      const analyzeResponse = await apiService.analyzeProblem(problem)
      
      if (!analyzeResponse.success) {
        throw new Error('Failed to analyze problem')
      }

      updateStep(0, 'completed')
      updateStep(1, 'completed') // Steps are generated as part of analysis
      
      setState(prev => ({
        ...prev,
        tutorialPlan: analyzeResponse.tutorialPlan
      }))

      // Step 2: Generate video
      updateStep(2, 'in-progress')
      
      const videoResponse = await apiService.generateVideo(analyzeResponse.tutorialPlan)
      
      if (!videoResponse.success) {
        throw new Error('Failed to start video generation')
      }

      setState(prev => ({
        ...prev,
        videoId: videoResponse.videoId
      }))

      updateStep(2, 'completed')
      updateStep(3, 'in-progress')

      // Step 3: Poll for video completion
      const pollVideoStatus = async (): Promise<VideoStatus> => {
        const status = await apiService.getVideoStatus(videoResponse.videoId)
        
        setState(prev => ({
          ...prev,
          videoStatus: status
        }))

        if (status.status === 'completed') {
          updateStep(3, 'completed')
          return status
        } else if (status.status === 'error') {
          throw new Error('Video generation failed')
        }

        // Continue polling
        await new Promise(resolve => setTimeout(resolve, 2000))
        return pollVideoStatus()
      }

      const finalStatus = await pollVideoStatus()
      
      setState(prev => ({
        ...prev,
        isGenerating: false,
        videoStatus: finalStatus
      }))

      return {
        tutorialPlan: analyzeResponse.tutorialPlan,
        videoId: videoResponse.videoId,
        videoStatus: finalStatus
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
      
      setState(prev => ({
        ...prev,
        isGenerating: false,
        error: errorMessage,
        steps: prev.steps.map((step, index) => 
          index === prev.currentStep ? { ...step, status: 'error' } : step
        )
      }))

      throw error
    }
  }, [updateStep])

  const downloadVideo = useCallback(async () => {
    if (!state.videoId) {
      throw new Error('No video available for download')
    }

    try {
      const blob = await apiService.downloadVideo(state.videoId)
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `${state.tutorialPlan?.title || 'tutorial'}.mp4`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('Failed to download video:', error)
      throw error
    }
  }, [state.videoId, state.tutorialPlan?.title])

  return {
    // State
    ...state,
    
    // Actions
    generateTutorial,
    downloadVideo,
    resetState,
    
    // Computed values
    isCompleted: state.steps.every(step => step.status === 'completed'),
    hasError: state.error !== null,
    completedSteps: state.steps.filter(step => step.status === 'completed').length,
    totalSteps: state.steps.length,
    progressPercentage: (state.steps.filter(step => step.status === 'completed').length / state.steps.length) * 100
  }
}

export default useTutorialGeneration
