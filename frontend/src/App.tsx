import ProblemInput from './components/ProblemInput'
import VideoPlayer from './components/VideoPlayer'
import ProgressTracker from './components/ProgressTracker'
import { useTutorialGeneration } from './hooks/useTutorialGeneration'

function App() {
  const {
    isGenerating,
    steps,
    tutorialPlan,
    videoStatus,
    error,
    generateTutorial,
    resetState
  } = useTutorialGeneration()

  const handleProblemSubmit = async (problem: string) => {
    try {
      await generateTutorial(problem)
    } catch (error) {
      console.error('Failed to generate tutorial:', error)
    }
  }

  const handleReset = () => {
    resetState()
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">
                CodeTutor AI
              </h1>
              <span className="ml-3 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                Beta
              </span>
            </div>
            <nav className="flex space-x-8">
              <a href="#" className="text-gray-500 hover:text-gray-900">
                How it works
              </a>
              <a href="#" className="text-gray-500 hover:text-gray-900">
                Examples
              </a>
              {(tutorialPlan || isGenerating) && (
                <button
                  onClick={handleReset}
                  className="text-gray-500 hover:text-gray-900"
                >
                  New Tutorial
                </button>
              )}
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            AI-Powered Coding Tutorials
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Describe your coding problem and get a personalized animated tutorial
            showing exactly what steps to take, what buttons to click, and what code to write.
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-8 max-w-2xl mx-auto">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    Generation Failed
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                  <div className="mt-4">
                    <button
                      onClick={handleReset}
                      className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Input Section */}
          <div className="space-y-6">
            <ProblemInput onSubmit={handleProblemSubmit} />
            <ProgressTracker steps={steps} />
          </div>

          {/* Video Section */}
          <div>
            <VideoPlayer
              videoUrl={videoStatus?.downloadUrl}
              isLoading={isGenerating}
            />
          </div>
        </div>
      </main>
    </div>
  )
}

export default App
